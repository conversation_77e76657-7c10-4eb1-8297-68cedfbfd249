#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文输入法自动安装脚本
支持Ubuntu/Debian系统一键安装中文输入法
优先尝试搜狗输入法，如不兼容则安装Fcitx5+中文输入法
作者: Assistant
日期: 2024-08-01
"""

import os
import sys
import subprocess
import requests
import re
from pathlib import Path
import tempfile
import shutil

class ChineseInputInstaller:
    def __init__(self):
        self.sogou_download_url = "https://pinyin.sogou.com/linux/download.php?f=linux&bit=64"
        self.temp_dir = tempfile.mkdtemp()
        self.deb_file = None
        self.use_fcitx5 = False

    def check_system(self):
        """检查系统兼容性"""
        print("🔍 检查系统兼容性...")

        # 检查是否为Linux系统
        if sys.platform != "linux":
            print("❌ 错误: 此脚本仅支持Linux系统")
            return False

        # 检查是否为Debian/Ubuntu系统
        try:
            result = subprocess.run(['lsb_release', '-i'], capture_output=True, text=True)
            if 'Ubuntu' not in result.stdout and 'Debian' not in result.stdout:
                print("⚠️  警告: 此脚本主要为Ubuntu/Debian系统设计")
        except FileNotFoundError:
            print("⚠️  无法检测系统版本，继续安装...")

        # 检查架构
        arch = subprocess.run(['uname', '-m'], capture_output=True, text=True).stdout.strip()
        if arch not in ['x86_64', 'amd64']:
            print(f"❌ 错误: 不支持的架构 {arch}，仅支持x86_64")
            return False

        print("✅ 系统检查通过")
        return True

    def install_basic_dependencies(self):
        """安装基础依赖"""
        print("📦 安装基础依赖...")

        basic_deps = ['wget', 'curl']

        try:
            subprocess.run(['sudo', 'apt', 'update'], check=True)
            subprocess.run(['sudo', 'apt', 'install', '-y'] + basic_deps, check=True)
            print("✅ 基础依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 基础依赖安装失败: {e}")
            return False

    def install_fcitx_dependencies(self):
        """安装fcitx依赖"""
        print("📦 安装fcitx依赖...")

        fcitx_deps = [
            'fcitx', 'fcitx-config-gtk',
            'fcitx-table-all', 'fcitx-frontend-all'
        ]

        try:
            subprocess.run(['sudo', 'apt', 'install', '-y'] + fcitx_deps, check=True)
            print("✅ fcitx依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ fcitx依赖安装失败: {e}")
            return False

    def install_fcitx5_dependencies(self):
        """安装fcitx5依赖"""
        print("� 安装fcitx5依赖...")

        fcitx5_deps = [
            'fcitx5', 'fcitx5-chinese-addons',
            'fcitx5-rime', 'fcitx5-config-qt'
        ]

        try:
            subprocess.run(['sudo', 'apt', 'install', '-y'] + fcitx5_deps, check=True)
            print("✅ fcitx5依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ fcitx5依赖安装失败: {e}")
            return False

    def try_install_sogou(self):
        """尝试安装搜狗输入法"""
        print("🔍 尝试安装搜狗输入法...")

        try:
            # 安装fcitx依赖
            if not self.install_fcitx_dependencies():
                return False

            # 下载搜狗输入法
            if not self.download_sogou_package():
                return False

            # 尝试安装
            if not self.install_sogou_package():
                print("⚠️  搜狗输入法安装失败，将使用fcitx5方案")
                return False

            # 配置搜狗输入法
            if not self.configure_sogou():
                return False

            print("✅ 搜狗输入法安装成功")
            return True

        except Exception as e:
            print(f"⚠️  搜狗输入法安装过程出错: {e}")
            return False

    def download_sogou_package(self):
        """下载搜狗输入法安装包"""
        print("📥 下载搜狗输入法安装包...")

        try:
            self.deb_file = os.path.join(self.temp_dir, "sogoupinyin.deb")

            # 使用wget下载
            subprocess.run([
                'wget', self.sogou_download_url, '-O', self.deb_file,
                '--progress=bar', '--show-progress'
            ], check=True)

            # 验证文件
            if not os.path.exists(self.deb_file) or os.path.getsize(self.deb_file) < 1024:
                raise Exception("下载的文件无效")

            print("✅ 搜狗输入法下载完成")
            return True

        except subprocess.CalledProcessError as e:
            print(f"❌ 搜狗输入法下载失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 搜狗输入法下载错误: {e}")
            return False

    def install_sogou_package(self):
        """安装搜狗输入法deb包"""
        print("🔧 安装搜狗输入法...")

        try:
            # 首先尝试直接安装
            result = subprocess.run(['sudo', 'dpkg', '-i', self.deb_file],
                                  capture_output=True, text=True)

            if result.returncode != 0:
                print("⚠️  检测到依赖问题，尝试修复...")
                # 尝试修复依赖
                fix_result = subprocess.run(['sudo', 'apt', '--fix-broken', 'install', '-y'],
                                          capture_output=True, text=True)

                if fix_result.returncode != 0:
                    print("❌ 依赖修复失败，搜狗输入法不兼容当前系统")
                    return False

                # 重新尝试安装
                result2 = subprocess.run(['sudo', 'dpkg', '-i', self.deb_file],
                                       capture_output=True, text=True)
                if result2.returncode != 0:
                    print("❌ 搜狗输入法安装失败，系统不兼容")
                    return False

            print("✅ 搜狗输入法安装完成")
            return True

        except subprocess.CalledProcessError as e:
            print(f"❌ 搜狗输入法安装失败: {e}")
            return False

    def configure_sogou(self):
        """配置搜狗输入法"""
        print("⚙️  配置搜狗输入法环境...")

        try:
            # 设置输入法为fcitx
            subprocess.run(['im-config', '-s', 'fcitx'], check=True)

            # 设置环境变量
            env_config = '''
# 搜狗输入法环境配置
export GTK_IM_MODULE=fcitx
export QT_IM_MODULE=fcitx
export XMODIFIERS=@im=fcitx
'''

            # 写入用户配置文件
            home = os.path.expanduser("~")
            bashrc_path = os.path.join(home, ".bashrc")

            with open(bashrc_path, 'a', encoding='utf-8') as f:
                f.write(env_config)

            # 创建自启动配置
            autostart_dir = os.path.join(home, ".config", "autostart")
            os.makedirs(autostart_dir, exist_ok=True)

            fcitx_desktop = os.path.join(autostart_dir, "fcitx.desktop")
            with open(fcitx_desktop, 'w', encoding='utf-8') as f:
                f.write('''[Desktop Entry]
Name=Fcitx
Exec=fcitx
Terminal=false
Type=Application
Categories=System;Utility;
StartupNotify=false
NoDisplay=true
''')

            print("✅ 搜狗输入法环境配置完成")
            return True

        except Exception as e:
            print(f"❌ 搜狗输入法配置失败: {e}")
            return False

    def install_fcitx5_method(self):
        """安装fcitx5中文输入法"""
        print("🔧 安装fcitx5中文输入法...")

        try:
            # 安装fcitx5依赖
            if not self.install_fcitx5_dependencies():
                return False

            # 配置fcitx5
            if not self.configure_fcitx5():
                return False

            print("✅ fcitx5中文输入法安装成功")
            self.use_fcitx5 = True
            return True

        except Exception as e:
            print(f"❌ fcitx5安装失败: {e}")
            return False

    def configure_fcitx5(self):
        """配置fcitx5"""
        print("⚙️  配置fcitx5环境...")

        try:
            # 设置输入法为fcitx5
            subprocess.run(['im-config', '-s', 'fcitx5'], check=True)

            # 设置环境变量
            env_config = '''
# Fcitx5 输入法环境配置
export GTK_IM_MODULE=fcitx5
export QT_IM_MODULE=fcitx5
export XMODIFIERS=@im=fcitx5
export SDL_IM_MODULE=fcitx5
export GLFW_IM_MODULE=ibus
'''

            # 写入用户配置文件
            home = os.path.expanduser("~")
            bashrc_path = os.path.join(home, ".bashrc")

            with open(bashrc_path, 'a', encoding='utf-8') as f:
                f.write(env_config)

            # 创建自启动配置
            autostart_dir = os.path.join(home, ".config", "autostart")
            os.makedirs(autostart_dir, exist_ok=True)

            fcitx5_desktop = os.path.join(autostart_dir, "fcitx5.desktop")
            with open(fcitx5_desktop, 'w', encoding='utf-8') as f:
                f.write('''[Desktop Entry]
Name=Fcitx5
Exec=fcitx5
Terminal=false
Type=Application
Categories=System;Utility;
StartupNotify=false
NoDisplay=true
''')

            # 启动fcitx5
            subprocess.run(['fcitx5', '&'], shell=True)

            print("✅ fcitx5环境配置完成")
            return True

        except Exception as e:
            print(f"❌ fcitx5配置失败: {e}")
            return False

    def cleanup(self):
        """清理临时文件"""
        try:
            shutil.rmtree(self.temp_dir)
            print("🧹 清理完成")
        except Exception as e:
            print(f"⚠️  清理失败: {e}")

    def install(self):
        """主安装流程"""
        print("🚀 开始安装中文输入法...")
        print("=" * 50)

        try:
            # 检查系统
            if not self.check_system():
                return False

            # 安装基础依赖
            if not self.install_basic_dependencies():
                return False

            # 首先尝试安装搜狗输入法
            print("📋 尝试安装搜狗输入法...")
            if self.try_install_sogou():
                print("=" * 50)
                print("🎉 搜狗输入法安装成功！")
                print("\n📋 后续步骤:")
                print("1. 重启系统或注销重新登录")
                print("2. 在系统设置中添加搜狗拼音输入法")
                print("3. 使用 Ctrl+Space 切换输入法")
                print("4. 右键输入法图标进行个性化设置")
                return True

            # 搜狗输入法安装失败，使用fcitx5方案
            print("\n📋 搜狗输入法不兼容，改用fcitx5方案...")
            if self.install_fcitx5_method():
                print("=" * 50)
                print("🎉 fcitx5中文输入法安装成功！")
                print("\n📋 后续步骤:")
                print("1. 重启系统或注销重新登录")
                print("2. 使用 Ctrl+Space 切换输入法")
                print("3. 运行 fcitx5-configtool 配置输入法")
                print("4. 添加中文输入法（拼音、五笔等）")
                return True
            else:
                print("❌ 所有输入法安装方案都失败了")
                return False

        except KeyboardInterrupt:
            print("\n❌ 用户取消安装")
            return False
        except Exception as e:
            print(f"❌ 安装过程中出现错误: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("""
中文输入法自动安装脚本

用法:
    python3 sogou.py          # 开始安装
    python3 sogou.py --help   # 显示帮助

功能:
- 自动检测系统兼容性
- 优先尝试安装搜狗输入法
- 如不兼容则自动安装fcitx5+中文输入法
- 自动配置输入法环境
- 一键完成所有安装步骤

支持的输入法:
1. 搜狗拼音输入法（优先）
2. fcitx5 + 中文输入法（备选）

注意事项:
- 需要sudo权限
- 确保网络连接正常
- 建议在安装前备份重要数据
        """)
        return

    installer = ChineseInputInstaller()
    success = installer.install()

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
