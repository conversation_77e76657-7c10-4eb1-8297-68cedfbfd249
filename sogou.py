#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜狗输入法自动安装脚本
支持Ubuntu/Debian系统一键安装最新版搜狗输入法
作者: Assistant
日期: 2024-08-01
"""

import os
import sys
import subprocess
import requests
import re
from pathlib import Path
import tempfile
import shutil

class SogouInstaller:
    def __init__(self):
        self.download_url = "https://ime.sogouimecdn.com/dl/gzindex/1571302197/sogoupinyin_4.0.1.2800_x86_64.deb"
        self.temp_dir = tempfile.mkdtemp()
        self.deb_file = None
        
    def check_system(self):
        """检查系统兼容性"""
        print("🔍 检查系统兼容性...")
        
        # 检查是否为Linux系统
        if sys.platform != "linux":
            print("❌ 错误: 此脚本仅支持Linux系统")
            return False
            
        # 检查是否为Debian/Ubuntu系统
        try:
            result = subprocess.run(['lsb_release', '-i'], capture_output=True, text=True)
            if 'Ubuntu' not in result.stdout and 'Debian' not in result.stdout:
                print("⚠️  警告: 此脚本主要为Ubuntu/Debian系统设计")
        except FileNotFoundError:
            print("⚠️  无法检测系统版本，继续安装...")
            
        # 检查架构
        arch = subprocess.run(['uname', '-m'], capture_output=True, text=True).stdout.strip()
        if arch not in ['x86_64', 'amd64']:
            print(f"❌ 错误: 不支持的架构 {arch}，仅支持x86_64")
            return False
            
        print("✅ 系统检查通过")
        return True
        
    def check_dependencies(self):
        """检查并安装依赖"""
        print("📦 检查系统依赖...")
        
        dependencies = [
            'wget', 'curl', 'fcitx', 'fcitx-config-gtk', 
            'fcitx-table-all', 'fcitx-frontend-all'
        ]
        
        missing_deps = []
        for dep in dependencies:
            result = subprocess.run(['dpkg', '-l', dep], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                missing_deps.append(dep)
                
        if missing_deps:
            print(f"📥 安装缺失依赖: {', '.join(missing_deps)}")
            try:
                subprocess.run(['sudo', 'apt', 'update'], check=True)
                subprocess.run(['sudo', 'apt', 'install', '-y'] + missing_deps, check=True)
                print("✅ 依赖安装完成")
            except subprocess.CalledProcessError as e:
                print(f"❌ 依赖安装失败: {e}")
                return False
        else:
            print("✅ 所有依赖已满足")
            
        return True
        
    def get_latest_version(self):
        """获取最新版本信息"""
        print("🔍 获取最新版本信息...")
        
        # 尝试从官网获取最新版本
        try:
            response = requests.get("https://pinyin.sogou.com/linux/", timeout=10)
            if response.status_code == 200:
                # 使用正则表达式查找下载链接
                pattern = r'href="([^"]*sogoupinyin[^"]*\.deb)"'
                matches = re.findall(pattern, response.text)
                if matches:
                    self.download_url = matches[0]
                    if not self.download_url.startswith('http'):
                        self.download_url = "https://pinyin.sogou.com" + self.download_url
                    print(f"✅ 找到最新版本下载链接")
                else:
                    print("⚠️  使用默认下载链接")
        except Exception as e:
            print(f"⚠️  获取最新版本失败，使用默认版本: {e}")
            
        return True
        
    def download_package(self):
        """下载安装包"""
        print("📥 下载搜狗输入法安装包...")
        
        try:
            # 获取文件名
            filename = self.download_url.split('/')[-1]
            if not filename.endswith('.deb'):
                filename = "sogoupinyin_latest.deb"
                
            self.deb_file = os.path.join(self.temp_dir, filename)
            
            # 使用wget下载
            subprocess.run([
                'wget', '-O', self.deb_file, self.download_url,
                '--progress=bar', '--show-progress'
            ], check=True)
            
            # 验证文件
            if not os.path.exists(self.deb_file) or os.path.getsize(self.deb_file) < 1024:
                raise Exception("下载的文件无效")
                
            print("✅ 下载完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 下载失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 下载错误: {e}")
            return False
            
    def install_package(self):
        """安装deb包"""
        print("🔧 安装搜狗输入法...")
        
        try:
            # 首先尝试直接安装
            result = subprocess.run(['sudo', 'dpkg', '-i', self.deb_file], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                print("⚠️  检测到依赖问题，正在修复...")
                # 修复依赖
                subprocess.run(['sudo', 'apt', '-f', 'install', '-y'], check=True)
                
                # 重新安装
                subprocess.run(['sudo', 'dpkg', '-i', self.deb_file], check=True)
                
            print("✅ 安装完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装失败: {e}")
            return False
            
    def configure_input_method(self):
        """配置输入法"""
        print("⚙️  配置输入法环境...")
        
        try:
            # 设置环境变量
            env_config = '''
# 搜狗输入法环境配置
export GTK_IM_MODULE=fcitx
export QT_IM_MODULE=fcitx
export XMODIFIERS=@im=fcitx
'''
            
            # 写入用户配置文件
            home = os.path.expanduser("~")
            bashrc_path = os.path.join(home, ".bashrc")
            
            with open(bashrc_path, 'a', encoding='utf-8') as f:
                f.write(env_config)
                
            # 创建自启动配置
            autostart_dir = os.path.join(home, ".config", "autostart")
            os.makedirs(autostart_dir, exist_ok=True)
            
            fcitx_desktop = os.path.join(autostart_dir, "fcitx.desktop")
            with open(fcitx_desktop, 'w', encoding='utf-8') as f:
                f.write('''[Desktop Entry]
Name=Fcitx
Exec=fcitx
Terminal=false
Type=Application
Categories=System;Utility;
StartupNotify=false
NoDisplay=true
''')
            
            print("✅ 环境配置完成")
            return True
            
        except Exception as e:
            print(f"❌ 配置失败: {e}")
            return False
            
    def cleanup(self):
        """清理临时文件"""
        try:
            shutil.rmtree(self.temp_dir)
            print("🧹 清理完成")
        except Exception as e:
            print(f"⚠️  清理失败: {e}")
            
    def install(self):
        """主安装流程"""
        print("🚀 开始安装搜狗输入法...")
        print("=" * 50)
        
        try:
            # 检查系统
            if not self.check_system():
                return False
                
            # 检查依赖
            if not self.check_dependencies():
                return False
                
            # 获取最新版本
            if not self.get_latest_version():
                return False
                
            # 下载安装包
            if not self.download_package():
                return False
                
            # 安装包
            if not self.install_package():
                return False
                
            # 配置输入法
            if not self.configure_input_method():
                return False
                
            print("=" * 50)
            print("🎉 搜狗输入法安装成功！")
            print("\n📋 后续步骤:")
            print("1. 重启系统或注销重新登录")
            print("2. 在系统设置中添加搜狗拼音输入法")
            print("3. 使用 Ctrl+Space 切换输入法")
            print("4. 右键输入法图标进行个性化设置")
            
            return True
            
        except KeyboardInterrupt:
            print("\n❌ 用户取消安装")
            return False
        except Exception as e:
            print(f"❌ 安装过程中出现错误: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("""
搜狗输入法自动安装脚本

用法:
    python3 sogou.py          # 开始安装
    python3 sogou.py --help   # 显示帮助

功能:
- 自动检测系统兼容性
- 自动安装所需依赖
- 下载最新版搜狗输入法
- 自动配置输入法环境
- 一键完成所有安装步骤

注意事项:
- 需要sudo权限
- 确保网络连接正常
- 建议在安装前备份重要数据
        """)
        return
        
    installer = SogouInstaller()
    success = installer.install()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
