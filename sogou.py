#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文输入法自动安装脚本
基于实际测试成功的安装步骤编写
只包含经过验证的fcitx5安装方法
作者: Assistant
日期: 2024-08-01
"""

import os
import sys
import subprocess

def run_command(cmd, description=""):
    """执行命令并显示结果"""
    if description:
        print(f"📋 {description}")

    try:
        if isinstance(cmd, str):
            result = subprocess.run(cmd, shell=True, check=True)
        else:
            result = subprocess.run(cmd, check=True)
        print("✅ 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 失败: {e}")
        return False

def main():
    """主安装流程 - 基于实际测试成功的步骤"""

    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("""
中文输入法自动安装脚本

用法:
    python3 sogou.py          # 开始安装
    python3 sogou.py --help   # 显示帮助

基于实际测试成功的安装步骤:
1. 更新系统包列表
2. 安装fcitx5和中文输入法包
3. 配置输入法系统
4. 设置环境变量
5. 设置自启动
6. 启动fcitx5

注意事项:
- 需要sudo权限
- 安装完成后需要重启系统
        """)
        return True

    print("🚀 开始安装中文输入法...")
    print("=" * 50)

    # 步骤1: 更新系统
    if not run_command(['sudo', 'apt', 'update'], "更新系统包列表"):
        return False

    # 步骤2: 安装fcitx5和中文输入法
    packages = ['fcitx5', 'fcitx5-chinese-addons', 'fcitx5-rime', 'fcitx5-config-qt']
    if not run_command(['sudo', 'apt', 'install', '-y'] + packages, "安装fcitx5和中文输入法"):
        return False

    # 步骤3: 配置输入法系统
    if not run_command(['im-config', '-s', 'fcitx5'], "配置输入法系统"):
        return False

    # 步骤4: 设置环境变量
    print("📋 设置环境变量")
    try:
        env_config = '''
# Fcitx5 输入法环境配置
export GTK_IM_MODULE=fcitx5
export QT_IM_MODULE=fcitx5
export XMODIFIERS=@im=fcitx5
export SDL_IM_MODULE=fcitx5
export GLFW_IM_MODULE=ibus
'''
        home = os.path.expanduser("~")
        bashrc_path = os.path.join(home, ".bashrc")

        with open(bashrc_path, 'a', encoding='utf-8') as f:
            f.write(env_config)
        print("✅ 成功")
    except Exception as e:
        print(f"❌ 失败: {e}")
        return False

    # 步骤5: 设置自启动
    print("📋 设置自启动")
    try:
        home = os.path.expanduser("~")
        autostart_dir = os.path.join(home, ".config", "autostart")
        os.makedirs(autostart_dir, exist_ok=True)

        fcitx5_desktop = os.path.join(autostart_dir, "fcitx5.desktop")
        with open(fcitx5_desktop, 'w', encoding='utf-8') as f:
            f.write('''[Desktop Entry]
Name=Fcitx5
Exec=fcitx5
Terminal=false
Type=Application
Categories=System;Utility;
StartupNotify=false
NoDisplay=true
''')
        print("✅ 成功")
    except Exception as e:
        print(f"❌ 失败: {e}")
        return False

    # 步骤6: 启动fcitx5
    if not run_command('fcitx5 &', "启动fcitx5"):
        print("⚠️  fcitx5启动可能失败，重启后应该正常工作")

    print("=" * 50)
    print("🎉 中文输入法安装完成！")
    print("\n📋 后续步骤:")
    print("1. 重启系统或注销重新登录")
    print("2. 使用 Ctrl+Space 切换输入法")
    print("3. 运行 fcitx5-configtool 配置输入法")
    print("4. 添加中文输入法（拼音、五笔等）")
    print("\n💡 成功安装的命令记录:")
    print("sudo apt update")
    print("sudo apt install -y fcitx5 fcitx5-chinese-addons fcitx5-rime fcitx5-config-qt")
    print("im-config -s fcitx5")
    print("# 环境变量已添加到 ~/.bashrc")
    print("# 自启动配置已创建")
    print("fcitx5 &")

    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
