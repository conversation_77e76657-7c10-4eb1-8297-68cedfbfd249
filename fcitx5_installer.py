#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fcitx5中文输入法自动安装脚本
支持Ubuntu/Debian系统一键安装Fcitx5中文输入法
基于实际测试成功的安装步骤编写
作者: Assistant
日期: 2024-08-01
"""

import os
import sys
import subprocess

class Fcitx5Installer:
    def __init__(self):
        pass

    def check_system(self):
        """检查系统兼容性"""
        print("🔍 检查系统兼容性...")
        
        # 检查是否为Linux系统
        if sys.platform != "linux":
            print("❌ 错误: 此脚本仅支持Linux系统")
            return False
            
        # 检查是否为Debian/Ubuntu系统
        try:
            result = subprocess.run(['lsb_release', '-i'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                distro = result.stdout.lower()
                if 'ubuntu' not in distro and 'debian' not in distro:
                    print("⚠️  警告: 此脚本主要为Ubuntu/Debian设计")
            else:
                print("⚠️  无法检测发行版，继续安装...")
        except FileNotFoundError:
            print("⚠️  无法检测发行版，继续安装...")
            
        # 检查架构
        try:
            result = subprocess.run(['uname', '-m'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                arch = result.stdout.strip()
                print(f"📋 系统架构: {arch}")
                if arch not in ['x86_64', 'amd64']:
                    print("⚠️  警告: 未在此架构上测试")
        except Exception:
            pass

        print("✅ 系统检查通过")
        return True

    def install_fcitx5_packages(self):
        """安装fcitx5和中文输入法包"""
        print("📦 更新系统包列表...")
        
        try:
            subprocess.run(['sudo', 'apt', 'update'], check=True)
            print("✅ 系统包列表更新完成")
        except subprocess.CalledProcessError as e:
            print(f"❌ 系统更新失败: {e}")
            return False
        
        print("📦 安装fcitx5和中文输入法...")
        
        fcitx5_packages = [
            'fcitx5', 
            'fcitx5-chinese-addons', 
            'fcitx5-rime', 
            'fcitx5-config-qt'
        ]
        
        try:
            subprocess.run(['sudo', 'apt', 'install', '-y'] + fcitx5_packages, check=True)
            print("✅ fcitx5和中文输入法安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ fcitx5安装失败: {e}")
            return False

    def configure_input_method(self):
        """配置输入法系统"""
        print("⚙️  配置输入法系统...")
        
        try:
            # 设置输入法为fcitx5
            subprocess.run(['im-config', '-s', 'fcitx5'], check=True)
            print("✅ 输入法系统配置完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 输入法系统配置失败: {e}")
            return False

    def setup_environment_variables(self):
        """设置环境变量"""
        print("⚙️  设置环境变量...")
        
        try:
            # 环境变量配置
            env_config = '''
# Fcitx5 输入法环境配置
export GTK_IM_MODULE=fcitx5
export QT_IM_MODULE=fcitx5
export XMODIFIERS=@im=fcitx5
export SDL_IM_MODULE=fcitx5
export GLFW_IM_MODULE=ibus
'''
            
            # 写入用户配置文件
            home = os.path.expanduser("~")
            bashrc_path = os.path.join(home, ".bashrc")
            
            with open(bashrc_path, 'a', encoding='utf-8') as f:
                f.write(env_config)
                
            print("✅ 环境变量设置完成")
            return True
            
        except Exception as e:
            print(f"❌ 环境变量设置失败: {e}")
            return False

    def setup_autostart(self):
        """设置自启动"""
        print("⚙️  设置自启动...")
        
        try:
            # 创建自启动目录
            home = os.path.expanduser("~")
            autostart_dir = os.path.join(home, ".config", "autostart")
            os.makedirs(autostart_dir, exist_ok=True)
            
            # 创建fcitx5自启动配置
            fcitx5_desktop = os.path.join(autostart_dir, "fcitx5.desktop")
            with open(fcitx5_desktop, 'w', encoding='utf-8') as f:
                f.write('''[Desktop Entry]
Name=Fcitx5
Exec=fcitx5
Terminal=false
Type=Application
Categories=System;Utility;
StartupNotify=false
NoDisplay=true
''')
            
            print("✅ 自启动设置完成")
            return True
            
        except Exception as e:
            print(f"❌ 自启动设置失败: {e}")
            return False

    def start_fcitx5(self):
        """启动fcitx5"""
        print("🚀 启动fcitx5...")
        
        try:
            # 启动fcitx5
            subprocess.run(['fcitx5', '&'], shell=True)
            print("✅ fcitx5启动完成")
            return True
        except Exception as e:
            print(f"❌ fcitx5启动失败: {e}")
            return False

    def install(self):
        """主安装流程"""
        print("🚀 开始安装Fcitx5中文输入法...")
        print("=" * 50)
        
        try:
            # 检查系统
            if not self.check_system():
                return False
                
            # 安装fcitx5包
            if not self.install_fcitx5_packages():
                return False
                
            # 配置输入法系统
            if not self.configure_input_method():
                return False
                
            # 设置环境变量
            if not self.setup_environment_variables():
                return False
                
            # 设置自启动
            if not self.setup_autostart():
                return False
                
            # 启动fcitx5
            if not self.start_fcitx5():
                return False
                
            print("=" * 50)
            print("🎉 Fcitx5中文输入法安装成功！")
            print("\n📋 后续步骤:")
            print("1. 重启系统或注销重新登录")
            print("2. 使用 Ctrl+Space 切换输入法")
            print("3. 运行 fcitx5-configtool 配置输入法")
            print("4. 添加中文输入法（拼音、五笔等）")
            print("\n💡 提示:")
            print("- 如果输入法无法使用，请重启系统")
            print("- 可以通过右键托盘图标进行设置")
            
            return True
            
        except KeyboardInterrupt:
            print("\n❌ 用户取消安装")
            return False
        except Exception as e:
            print(f"❌ 安装过程中出现错误: {e}")
            return False

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("""
Fcitx5中文输入法自动安装脚本

用法:
    python3 fcitx5_installer.py          # 开始安装
    python3 fcitx5_installer.py --help   # 显示帮助

功能:
- 自动检测系统兼容性
- 安装fcitx5和中文输入法包
- 自动配置输入法环境
- 设置环境变量和自启动
- 一键完成所有安装步骤

支持的输入法:
- 拼音输入法
- Rime输入法引擎
- 其他中文输入法

注意事项:
- 需要sudo权限
- 确保网络连接正常
- 建议在安装前备份重要数据
- 安装完成后需要重启系统
        """)
        return
        
    installer = Fcitx5Installer()
    success = installer.install()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
